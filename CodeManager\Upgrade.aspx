<%@ Page Title="升级VIP会员" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Upgrade.aspx.cs" Inherits="Account.Web.Upgrade" %>
<%@ Import Namespace="CommonLib" %>
<%@ Import Namespace="System.Collections.Generic" %>
<%@ Import Namespace="System.Linq" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
<meta name="description" content="升级OCR文字识别助手VIP会员，享受更多专业功能，多种版本选择满足不同需求">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<style>
body{min-height:100vh}
.upgrade-container{max-width:1200px;margin:0 auto;padding:20px;padding-top:65px;min-height:calc(100vh - 100px);position:relative}
.main-content{display:flex;gap:30px;align-items:flex-start}
.left-section{flex:2;background:linear-gradient(135deg,#ffffff 0%,#fafbfc 100%);border-radius:16px;padding:30px;box-shadow:0 8px 32px rgba(0,0,0,0.12);border:1px solid rgba(255,255,255,0.9);backdrop-filter:blur(10px)}
.right-section{flex:1;background:linear-gradient(135deg,#ffffff 0%,#fafbfc 100%);border-radius:16px;padding:30px;box-shadow:0 8px 32px rgba(0,0,0,0.12);border:1px solid rgba(255,255,255,0.9);position:sticky;top:20px;backdrop-filter:blur(10px)}
.section-title{font-size:1.3rem;font-weight:bold;color:#333;margin-bottom:20px;display:flex;align-items:center;justify-content:space-between}
.version-tabs{display:flex;gap:15px;margin-bottom:30px;flex-wrap:wrap}
.version-tab{flex:1;min-width:120px;padding:16px 16px 20px;border:2px solid #e9ecef;border-radius:12px;text-align:center;cursor:pointer;transition:all 0.3s ease;background:white;position:relative;overflow:hidden}
.version-tab:hover{transform:translateY(-3px);box-shadow:0 6px 20px rgba(0,0,0,0.12)}
.version-tab.active{transform:translateY(-3px);box-shadow:0 8px 24px rgba(0,0,0,0.18);border-width:3px}
.version-tab.v-1{border-color:#6666FF;backdrop-filter:blur(10px)}
.version-tab.v-1:hover{border-color:#6666FF;background:rgba(102,102,255,0.08);box-shadow:0 6px 20px rgba(102,102,255,0.25)}
.version-tab.v1{border-color:#4B4B4B;backdrop-filter:blur(10px)}
.version-tab.v1:hover{border-color:#4B4B4B;background:rgba(75,75,75,0.08);box-shadow:0 6px 20px rgba(75,75,75,0.25)}
.version-tab.v3{border-color:#E6D700;backdrop-filter:blur(10px)}
.version-tab.v3:hover{border-color:#E6D700;background:rgba(255,235,193,0.15);box-shadow:0 6px 20px rgba(255,215,0,0.25)}
.version-recommend-badge{position:absolute;top:-2px;right:-2px;background:#ff4757;color:white;padding:4px 10px;border-radius:0 12px 0 12px;font-size:11px;font-weight:600;z-index:2;box-shadow:0 2px 6px rgba(255,71,87,0.4);letter-spacing:0.5px}
.version-tab.v-1.active{background:linear-gradient(89.95deg,#6666FF 11.5%,#38c0ff 100.01%);border-color:#6666FF;color:white;box-shadow:0 4px 12px rgba(102,102,255,0.3)}
.smart-recommendation{background:linear-gradient(135deg,rgba(102,126,234,0.12) 0%,rgba(118,75,162,0.12) 100%);border-radius:16px;padding:22px 20px;margin-bottom:28px;text-align:center;border:2px solid rgba(102,126,234,0.3);position:relative;overflow:hidden;transition:all 0.3s ease;box-shadow:0 8px 24px rgba(102,126,234,0.18);backdrop-filter:blur(10px)}
.smart-recommendation::before{content:'';position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.4),transparent);animation:shimmer 3s infinite}
@keyframes shimmer{0%{left:-100%}100%{left:100%}}
.smart-recommendation:hover{transform:translateY(-4px);box-shadow:0 12px 35px rgba(102,126,234,0.28);border-color:rgba(102,126,234,0.45);background:linear-gradient(135deg,rgba(102,126,234,0.15) 0%,rgba(118,75,162,0.15) 100%)}
.recommendation-trigger{display:flex;align-items:center;justify-content:center;gap:12px;flex-wrap:wrap}
.recommendation-trigger .fa-robot{animation:robotPulse 2s ease-in-out infinite}
@keyframes robotPulse{0%,100%{transform:scale(1);color:#007cfa}50%{transform:scale(1.1);color:#667eea}}
@keyframes sparkle{0%,100%{transform:scale(1) rotate(0deg);opacity:1}25%{transform:scale(1.1) rotate(-5deg);opacity:0.8}75%{transform:scale(1.05) rotate(5deg);opacity:0.9}}
.quiz-btn:hover{transform:translateY(-2px);box-shadow:0 6px 16px rgba(102,126,234,0.45)!important;background:linear-gradient(135deg,#7c8df0 0%,#8a5fb8 100%)!important}

.version-features-section.v-1{background:transparent;border:1px solid rgba(102,102,255,0.03)}
.version-features-section.v-1 .features-title{color:#4444BB;font-weight:600;font-size:17px}
.version-features-section.v-1 .features-compare-link{color:#6666FF}
.version-features-section.v-1 .features-compare-link:hover{color:#5555EE}
.version-features-section.v-1 .feature-item:hover{background:rgba(102,102,255,0.04)}
.version-features-section.v-1 .feature-item i{color:#6666FF}
.version-features-section.v-1 .expand-btn{border-color:#6666FF;color:#6666FF;background:rgba(255,255,255,0.9)}
.version-features-section.v-1 .expand-btn:hover{border-color:#5555EE;color:#5555EE;background:rgba(255,255,255,1)}
.pricing-item.selected.v-1{background:linear-gradient(89.95deg,#6666FF 11.5%,#38c0ff 100.01%);border:none;color:white;box-shadow:0 2px 8px rgba(102,102,255,0.25)}
.pricing-item.selected.v-1 .pricing-name,.pricing-item.selected.v-1 .current-price{color:white}
.pricing-item.selected.v-1 .original-price{color:rgba(255,255,255,0.8)}
.pricing-item.selected.v-1 .discount-percentage{color:white;background:rgba(255,255,255,0.2)}
.pricing-item.selected.v-1 .daily-cost{color:rgba(255,255,255,0.9)}
.pricing-item.v-1:hover:not(.selected){border-color:#6666FF;background:linear-gradient(135deg,rgba(102,102,255,0.08) 0%,rgba(102,102,255,0.12) 100%);transform:translateY(-2px);box-shadow:0 4px 16px rgba(102,102,255,0.2)}
.btn-upgrade.v-1{background:linear-gradient(89.95deg,#6666FF 11.5%,#38c0ff 100.01%);border:none;color:white;box-shadow:0 2px 8px rgba(102,102,255,0.3)}
.btn-upgrade.v-1:hover{background:linear-gradient(89.95deg,#5555EE 11.5%,#2AAFEE 100.01%);transform:translateY(-1px);box-shadow:0 4px 12px rgba(102,102,255,0.4)}
.version-tab.v1.active{background:linear-gradient(to right,#4B4B4B 5.77%,#1A1510 100%);border-color:#4B4B4B;color:#F9D9A8;box-shadow:0 4px 12px rgba(75,75,75,0.3)}
.version-features-section.v1{background:transparent;border:1px solid rgba(75,75,75,0.03)}
.version-features-section.v1 .features-title{color:#444444;font-weight:600;font-size:17px}
.version-features-section.v1 .features-compare-link{color:#4B4B4B}
.version-features-section.v1 .features-compare-link:hover{color:#3A3A3A}
.version-features-section.v1 .feature-item:hover{background:rgba(75,75,75,0.04)}
.version-features-section.v1 .feature-item i{color:#4B4B4B}
.version-features-section.v1 .expand-btn{border-color:#4B4B4B;color:#4B4B4B;background:rgba(255,255,255,0.9)}
.version-features-section.v1 .expand-btn:hover{border-color:#3A3A3A;color:#3A3A3A;background:rgba(255,255,255,1)}
.pricing-item.selected.v1{background:linear-gradient(to right,#4B4B4B 5.77%,#1A1510 100%);border:none;color:#F9D9A8;box-shadow:0 2px 8px rgba(75,75,75,0.3)}
.pricing-item.selected.v1 .pricing-name,.pricing-item.selected.v1 .current-price{color:#F9D9A8}
.pricing-item.selected.v1 .original-price{color:rgba(249,217,168,0.7)}
.pricing-item.selected.v1 .discount-percentage{color:#F9D9A8;background:rgba(249,217,168,0.2)}
.pricing-item.selected.v1 .daily-cost{color:rgba(249,217,168,0.9)}
.pricing-item.v1:hover:not(.selected){border-color:#4B4B4B;background:linear-gradient(135deg,rgba(75,75,75,0.08) 0%,rgba(75,75,75,0.12) 100%);transform:translateY(-2px);box-shadow:0 4px 16px rgba(75,75,75,0.2)}
.btn-upgrade.v1{background:linear-gradient(to right,#4B4B4B 5.77%,#1A1510 100%);border:none;color:#F9D9A8;box-shadow:0 2px 8px rgba(75,75,75,0.3)}
.btn-upgrade.v1:hover{background:linear-gradient(to right,#3A3A3A 5.77%,#0F0A06 100%);transform:translateY(-1px);box-shadow:0 4px 12px rgba(75,75,75,0.4)}
.version-tab.v3.active{background:linear-gradient(135deg,#FFF8E7 0%,#FFE4B5 50%,#FFDB9A 100%);border-color:#E6D700;color:#944800;box-shadow:0 4px 12px rgba(255,215,0,0.3);position:relative}
.version-tab.v3.active::before{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(135deg,rgba(255,255,255,0.3) 0%,transparent 50%);border-radius:6px;pointer-events:none}
.version-features-section.v3{background:transparent;border:1px solid rgba(230,215,0,0.03)}
.version-features-section.v3 .features-title{color:#886622;font-weight:600;font-size:17px}
.version-features-section.v3 .features-compare-link{color:#944800}
.version-features-section.v3 .features-compare-link:hover{color:#7A3600}
.version-features-section.v3 .feature-item:hover{background:rgba(230,215,0,0.04)}
.version-features-section.v3 .feature-item i{color:#E6D700}
.version-features-section.v3 .expand-btn{border-color:#944800;color:#944800;background:rgba(255,255,255,0.9)}
.version-features-section.v3 .expand-btn:hover{border-color:#7A3600;color:#7A3600;background:rgba(255,255,255,1)}
.pricing-item.selected.v3{background:linear-gradient(135deg,#FFF8E7 0%,#FFE4B5 50%,#FFDB9A 100%);border:none;color:#944800;box-shadow:0 2px 8px rgba(255,215,0,0.3)}
.pricing-item.selected.v3 .pricing-name,.pricing-item.selected.v3 .current-price{color:#944800}
.pricing-item.selected.v3 .original-price{color:rgba(148,72,0,0.7)}
.pricing-item.selected.v3 .discount-percentage{color:#944800;background:rgba(148,72,0,0.15)}
.pricing-item.selected.v3 .daily-cost{color:rgba(148,72,0,0.8)}
.pricing-item.v3:hover:not(.selected){border-color:#E6D700;background:linear-gradient(135deg,rgba(255,235,193,0.12) 0%,rgba(255,215,0,0.08) 100%);transform:translateY(-2px);box-shadow:0 4px 16px rgba(255,215,0,0.2)}
.btn-upgrade.v3{background:linear-gradient(135deg,#FFF8E7 0%,#FFE4B5 50%,#FFDB9A 100%);border:none;color:#944800;font-weight:bold;box-shadow:0 2px 8px rgba(255,215,0,0.3)}
.btn-upgrade.v3:hover{background:linear-gradient(135deg,#FFE4B5 0%,#FFDB9A 50%,#FFD700 100%);transform:translateY(-1px);box-shadow:0 4px 12px rgba(255,215,0,0.4)}

.btn-upgrade{transition:all 0.3s ease;border-radius:6px;padding:12px 24px;font-size:16px;font-weight:500;position:relative;overflow:hidden}
.btn-upgrade::before{content:'';position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.2),transparent);transition:left 0.5s;z-index:1}
.btn-upgrade:hover::before{left:100%}
.version-tab{animation:fadeIn 0.4s ease-out}
.version-tab:nth-child(1){animation-delay:0.05s}
.version-tab:nth-child(2){animation-delay:0.1s}
.version-tab:nth-child(3){animation-delay:0.15s}
.trust-elements{animation:fadeIn 0.8s ease-out 0.5s both}
@keyframes fadeIn{from{opacity:0}to{opacity:1}}
.feature-item{transition:all 0.2s ease}
.feature-item:hover{transform:translateX(4px)}
.security-badges .badge-item:hover{transform:translateY(-1px);box-shadow:0 2px 8px rgba(40,167,69,0.2)}
.pricing-item{height:120px;padding:16px 20px;border:2px solid #e9ecef;border-radius:16px;text-align:center;cursor:pointer;transition:all 0.3s ease;background:linear-gradient(135deg,#ffffff 0%,#fafbfc 100%);margin:0 0 12px 0;position:relative;overflow:hidden;display:flex;flex-direction:column;justify-content:center;box-shadow:0 6px 20px rgba(0,0,0,0.1);backdrop-filter:blur(3px)}
.pricing-name{font-size:1rem;font-weight:700;margin-bottom:8px;color:#333}
.pricing-price{display:flex;align-items:center;justify-content:center;gap:8px;margin-bottom:6px;flex-wrap:wrap}
.current-price{font-size:1.5rem;font-weight:bold;color:#333;position:relative}
.original-price{font-size:1rem;color:#999;text-decoration:line-through;opacity:0.8}
.discount-percentage{font-size:0.85rem;color:#ff4757;font-weight:600;background:rgba(255,71,87,0.1);padding:2px 6px;border-radius:12px;margin-left:4px}
.daily-cost{font-size:0.75rem;color:#666;margin-top:2px;font-style:italic}
.pricing-item.selected{box-shadow:0 8px 24px rgba(0,0,0,0.15);border:none}
.pricing-tag{position:absolute;top:-2px;left:12px;padding:4px 12px;border-radius:0 0 12px 12px;font-size:11px;font-weight:bold;z-index:3;color:white;text-shadow:0 1px 2px rgba(0,0,0,0.2)}
.pricing-tag.hot{background:linear-gradient(135deg,#ff6b35 0%,#ff4757 100%);box-shadow:0 3px 8px rgba(255,71,87,0.4)}
.pricing-tag.recommend{background:linear-gradient(135deg,#ff6348 0%,#e55039 100%);box-shadow:0 3px 8px rgba(229,80,57,0.4)}
.pricing-tag.new{background:linear-gradient(135deg,#2ed573 0%,#1dd1a1 100%);box-shadow:0 3px 8px rgba(29,209,161,0.4)}
.discount-badge{position:absolute;top:-2px;right:-2px;background:linear-gradient(135deg,#ff4757 0%,#ff3742 100%);color:white;padding:6px 12px;border-radius:0 12px 0 16px;font-size:13px;font-weight:bold;z-index:2;box-shadow:0 3px 8px rgba(255,71,87,0.4);text-shadow:0 1px 2px rgba(0,0,0,0.2)}
.version-tab .tab-icon{width:32px;height:32px;margin:0 auto 8px;background-size:contain;background-repeat:no-repeat;background-position:center}
.version-tab .tab-name{font-weight:bold;font-size:1rem}
.pricing-section{margin-bottom:30px}
.pricing-options{display:none}
.pricing-options.active{display:grid;grid-template-columns:repeat(3,1fr);gap:16px}
.version-features-section{margin:20px 0;border-radius:12px;transition:border-color 0.3s ease,box-shadow 0.3s ease;background:linear-gradient(135deg,rgba(255,255,255,0.8) 0%,rgba(250,251,252,0.8) 100%);border:1px solid rgba(200,200,200,0.3);box-shadow:0 4px 16px rgba(0,0,0,0.06);overflow:hidden;backdrop-filter:blur(3px)}
.features-header{display:flex;justify-content:space-between;align-items:center;padding:16px 16px 12px;margin:0;border-bottom:none;background:transparent}
.features-title{font-size:16px;font-weight:600;margin:0;color:#333}
.features-compare-link{color:#007cfa;text-decoration:none;font-size:14px;transition:color 0.3s ease;font-weight:400;cursor:pointer}
.features-compare-link:hover{color:#0056b3;text-decoration:none}
.compare-modal{display:none;position:fixed;z-index:1000;left:0;top:0;width:100%;height:100%;background-color:rgba(0,0,0,0.5)}
.compare-modal-content{background-color:#fefefe;margin:1% auto;padding:0;border-radius:16px;width:90%;max-width:1400px;height:95vh;overflow:hidden;box-shadow:0 12px 48px rgba(0,0,0,0.15);display:flex;flex-direction:column}
.compare-modal-header{background:linear-gradient(135deg,#f8f9fa 0%,#e9ecef 100%);padding:14px 32px;border-bottom:1px solid #dee2e6;display:flex;justify-content:space-between;align-items:center;flex-shrink:0;border-radius:16px 16px 0 0}
.compare-modal-title{font-size:22px;font-weight:600;color:#2c3e50;margin:0;display:flex;align-items:center}
.compare-modal-title::before{content:"📊";margin-right:8px;font-size:20px}
.compare-modal-close{color:#6c757d;font-size:32px;font-weight:bold;cursor:pointer;line-height:1;padding:8px;border-radius:50%;transition:all 0.3s ease;display:flex;align-items:center;justify-content:center;width:48px;height:48px}
.compare-modal-close:hover{color:#dc3545;background-color:rgba(220,53,69,0.1);transform:scale(1.1)}
.compare-modal-body{padding:0;flex:1;overflow:hidden;border-radius:0 0 16px 16px}
.compare-modal-body iframe{width:100%;height:100%;border:none;border-radius:0 0 16px 16px}
@media(max-width:1200px){.compare-modal-content{width:95%;max-width:none}}
@media(max-width:768px){.compare-modal-content{width:98%;height:98vh;margin:1% auto;border-radius:12px}.compare-modal-header{padding:16px 20px;border-radius:12px 12px 0 0}.compare-modal-title{font-size:18px}.compare-modal-close{width:40px;height:40px;font-size:28px}.compare-modal-body iframe{border-radius:0 0 12px 12px}}
.scenario-tags{display:flex;flex-wrap:wrap;gap:6px}
.scenario-tag{background:rgba(0,123,250,0.1);color:#007cfa;padding:4px 8px;border-radius:12px;font-size:11px;border:1px solid rgba(0,123,250,0.2)}
.features-body{padding:8px 16px 12px;background:transparent}
.features-grid{display:grid;grid-template-columns:repeat(2,1fr);gap:3px 8px;margin:0}
.feature-item{display:flex;align-items:flex-start;padding:6px 0;background:transparent;border:none;font-size:14px;transition:background-color 0.2s ease}
.feature-item:hover{background:rgba(255,255,255,0.4);border-radius:4px}
.feature-item i{color:#28a745;margin-right:6px;font-size:14px;margin-top:1px;flex-shrink:0}
.feature-item .feature-content{flex:1}
.feature-item .feature-name{font-weight:500;color:#333;margin-bottom:1px;line-height:1.2;font-size:14px}
.feature-item .feature-desc{color:#666;font-size:12px;line-height:1.2}
.features-footer{padding:8px 16px 16px;background:transparent;border-top:none;text-align:center}
.expand-btn{background:rgba(255,255,255,0.9);border:1px solid #007cfa;color:#007cfa;font-size:13px;cursor:pointer;display:inline-flex;align-items:center;justify-content:center;transition:all 0.2s ease;padding:6px 14px;border-radius:14px;font-weight:400;box-shadow:0 1px 3px rgba(0,0,0,0.08)}
.expand-btn:hover{color:#0056b3;background:rgba(255,255,255,1);border-color:#0056b3;box-shadow:0 2px 6px rgba(0,0,0,0.12);transform:translateY(-1px)}
.expand-btn i{margin-left:4px;transition:transform 0.3s ease;font-size:12px}
.expand-btn.expanded i{transform:rotate(180deg)}
.order-summary{border:1px solid rgba(0,123,250,0.15);border-radius:16px;padding:24px;margin-bottom:24px;background:linear-gradient(135deg,#ffffff 0%,#fafbfc 100%);box-shadow:0 6px 24px rgba(0,0,0,0.08);backdrop-filter:blur(5px)}
.summary-item{display:flex;justify-content:space-between;align-items:center;margin-bottom:16px;padding:8px 0}
.summary-label{color:#666;font-size:16px;font-weight:600}
.summary-value{font-weight:600;color:#333;font-size:15px}
.summary-item.discount .summary-value{color:#ff4757}
.summary-divider{height:2px;background:linear-gradient(90deg,transparent,#e9ecef,transparent);margin:20px 0}
.summary-total{display:flex;justify-content:space-between;align-items:center;margin-top:20px;padding-top:20px;border-top:2px solid #007cfa}
.total-label{font-size:1.1rem;font-weight:bold;color:#333}
.total-price{font-size:1.5rem;font-weight:bold;color:#007cfa}
.order-total{padding:20px 0;border-top:2px solid rgba(0,123,250,0.1);background:linear-gradient(135deg,rgba(0,123,250,0.02) 0%,rgba(0,123,250,0.05) 100%);border-radius:12px;margin:-8px -8px 0 -8px;padding:20px 16px}
.total-row{display:flex;justify-content:space-between;align-items:center}
.total-prices{text-align:right;display:flex;flex-direction:column;align-items:flex-end;gap:4px}
.discount-info{color:#ff6600;font-size:13px;font-weight:600;background:linear-gradient(135deg,rgba(255,102,0,0.1) 0%,rgba(255,102,0,0.15) 100%);padding:4px 10px;border-radius:16px;border:1px solid rgba(255,102,0,0.2);animation:pulse-discount 2s infinite}
@keyframes pulse-discount{0%,100%{transform:scale(1)}50%{transform:scale(1.02)}}
@keyframes pulse{0%,100%{opacity:1}50%{opacity:0.6}}
.original-total{color:#999;font-size:18px;text-decoration:line-through;opacity:0.8;font-weight:600}
.current-total{color:#007cfa;font-size:40px;font-weight:900;text-shadow:0 2px 6px rgba(0,123,250,0.3);line-height:1;animation:price-pulse 3s ease-in-out infinite}
@keyframes price-pulse{0%,100%{transform:scale(1)}50%{transform:scale(1.02)}}
.trust-elements{margin-bottom:30px;padding:24px;background:linear-gradient(135deg,#ffffff 0%,#fafbfc 100%);border-radius:16px;border:1px solid rgba(0,123,250,0.12);box-shadow:0 6px 24px rgba(0,0,0,0.08);backdrop-filter:blur(5px)}
.social-proof{margin-bottom:20px;text-align:center}
.user-stats{display:flex;align-items:center;justify-content:center;margin-bottom:12px;font-size:15px;color:#333;font-weight:500}
.user-stats i{color:#007cfa;margin-right:10px;font-size:18px;animation:pulse 2s infinite}
.user-stats strong{color:#007cfa;font-size:16px;font-weight:700}
.user-count{display:inline-block;min-width:60px;text-align:center}
.rating-info{display:flex;align-items:center;justify-content:center;gap:10px;font-size:14px;color:#666}
.stars{display:flex;gap:3px}
.stars i{color:#ffc107;font-size:16px;transition:transform 0.2s ease}
.stars i:hover{transform:scale(1.1)}
.rating-text{font-weight:500}
.security-badges{display:grid;grid-template-columns:1fr;gap:10px;margin-top:16px}
.badge-item{display:flex;align-items:center;padding:12px 16px;background:linear-gradient(135deg,rgba(40,167,69,0.08) 0%,rgba(40,167,69,0.12) 100%);border-radius:12px;font-size:13px;color:#28a745;border:1px solid rgba(40,167,69,0.15);transition:all 0.3s ease;font-weight:500}
.badge-item:hover{transform:translateY(-2px);box-shadow:0 4px 12px rgba(40,167,69,0.2);background:linear-gradient(135deg,rgba(40,167,69,0.12) 0%,rgba(40,167,69,0.16) 100%)}
.badge-item i{margin-right:8px;font-size:16px}
.account-input{margin:24px 0}
.account-input .form-control{width:100%;padding:16px;border:2px solid #e9ecef;border-radius:12px;font-size:15px;transition:all 0.3s ease;background:rgba(255,255,255,0.8)}
.account-input .form-control:focus{border-color:#007cfa;box-shadow:0 0 0 3px rgba(0,123,250,0.1);outline:none;background:white}
.account-input .form-control::placeholder{color:#999;font-weight:400}
.btn-upgrade{width:100%;padding:18px;background:linear-gradient(135deg,#007cfa 0%,#0056b3 100%);color:white;border:none;border-radius:12px;font-size:16px;font-weight:600;cursor:pointer;transition:all 0.3s ease;position:relative;overflow:hidden;box-shadow:0 4px 16px rgba(0,123,250,0.3);text-transform:uppercase;letter-spacing:0.5px}
.btn-upgrade:hover{background:linear-gradient(135deg,#0056b3 0%,#004494 100%);transform:translateY(-2px);box-shadow:0 6px 20px rgba(0,123,250,0.4)}
.btn-upgrade:active{transform:translateY(0);box-shadow:0 2px 8px rgba(0,123,250,0.3)}
.btn-upgrade::before{content:'';position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.2),transparent);transition:left 0.5s;z-index:1}
.btn-upgrade:hover::before{left:100%}
.btn-upgrade.loading{pointer-events:none;opacity:0.8}
.btn-upgrade.loading::after{content:'';position:absolute;top:50%;left:50%;width:20px;height:20px;margin:-10px 0 0 -10px;border:2px solid transparent;border-top:2px solid white;border-radius:50%;animation:spin 1s linear infinite;z-index:2}
@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}
@keyframes shine{0%{left:-100%}100%{left:100%}}
.icon-v-1{background:url(/static/image/vip_1.png) 0 center no-repeat;background-size:contain}
.icon-v1{background:url(/static/image/vip_2.png) 0 center no-repeat;background-size:contain}
.icon-v3{background:url(/static/image/vip_3.png) 0 center no-repeat;background-size:contain}
@media(max-width:768px){
.upgrade-container{padding:15px 10px;padding-top:60px}
.main-content{flex-direction:column;gap:20px}
.left-section,.right-section{padding:20px 16px;border-radius:12px;box-shadow:0 6px 24px rgba(0,0,0,0.1)}
.right-section{position:static;margin-top:20px}
.version-tabs{flex-direction:column;gap:12px}
.version-tab{min-width:auto;padding:12px 16px 16px;margin-bottom:0}
.version-tab .tab-icon{width:28px;height:28px;margin-bottom:6px}
.version-tab .tab-name{font-size:0.95rem}
.version-recommend-badge{padding:3px 8px;font-size:10px}
.pricing-options{grid-template-columns:1fr;gap:12px}
.pricing-item{height:auto;min-height:100px;padding:14px 16px}
.pricing-name{font-size:0.95rem}
.current-price{font-size:1.3rem}
.original-price{font-size:0.9rem}
.discount-percentage{font-size:0.8rem}
.daily-cost{font-size:0.7rem}
.discount-badge{padding:4px 8px;font-size:11px}
.features-grid{grid-template-columns:1fr;gap:6px}
.version-features-section{margin:15px 0;border-radius:8px}
.features-header{padding:12px 16px}
.features-body{padding:8px 16px 12px}
.features-footer{padding:8px 16px 12px}
.feature-item{padding:8px 0;font-size:14px}
.feature-item .feature-name{font-size:14px}
.feature-item .feature-desc{font-size:12px}
.expand-btn{padding:8px 16px;font-size:13px}
.trust-elements{padding:20px;margin-bottom:24px;border-radius:12px}
.user-stats{font-size:14px}
.user-stats i{font-size:16px}
.rating-info{font-size:13px}
.stars i{font-size:15px}
.security-badges{grid-template-columns:1fr;gap:8px}
.badge-item{padding:10px 12px;font-size:12px}
.badge-item i{font-size:14px}
.order-summary{padding:20px;border-radius:12px}
.summary-item{margin-bottom:14px}
.summary-label{font-size:15px}
.summary-value{font-size:14px}
.order-total{padding:16px 12px;margin:-6px -6px 0 -6px}
.current-total{font-size:36px}
.discount-info{font-size:10px;padding:1px 6px}
.original-total{font-size:12px}
.account-input .form-control{padding:14px;font-size:14px}
.btn-upgrade{padding:16px;font-size:15px}
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="upgrade-container">
        <div class="main-content">
            <div class="left-section">
                <div class="smart-recommendation">
                    <div class="recommendation-trigger">
                        <i class="fa fa-robot" style="color: #007cfa; margin-right: 8px; font-size: 16px;"></i>
                        <span style="color: #666; margin-right: 12px;">不知道选哪个？</span>
                        <button type="button" class="quiz-btn" onclick="openSmartRecommendationIframe(); return false;" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 12px 24px; border-radius: 25px; font-size: 14px; font-weight: 600; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 12px rgba(102, 126, 234, 0.35); position: relative; overflow: hidden;">
                            <i class="fa fa-magic" style="margin-right: 6px; animation: sparkle 1.5s ease-in-out infinite;"></i>让AI帮我选择合适的版本
                        </button>
                    </div>
                </div>
                <div class="section-title">
                    选择升级类型
                </div>

                <div class="version-tabs" id="versionTabs">
                    <!-- 版本选项卡将通过Ajax动态加载 -->
                </div>

                <div class="version-features-section" id="versionFeaturesSection">
                    <div class="features-header">
                        <h3 class="features-title" id="featuresTitle">专属功能</h3>
                        <a href="javascript:void(0);" class="features-compare-link" onclick="openCompareModal()"><i class="fa fa-info-circle"></i>&nbsp;功能比对 ></a>
                    </div>
                    <div class="features-body">
                        <div class="features-grid" id="featuresGrid"></div>
                        <div class="use-cases" id="useCases" style="margin-top: 16px; padding-top: 16px; border-top: 1px solid rgba(0,0,0,0.1);">
                            <h4 style="font-size: 14px; color: #666; margin-bottom: 8px; font-weight: 600;">
                                <i class="fa fa-lightbulb" style="margin-right: 6px; color: #ffc107;"></i>适用场景
                            </h4>
                            <div class="scenario-tags" id="scenarioTags"><span class="scenario-tag">扫描文档</span><span class="scenario-tag">提取图片文字</span><span class="scenario-tag">学习笔记整理</span><span class="scenario-tag">日常办公</span></div>
                        </div>
                    </div>
                    <div class="features-footer" id="featuresExpand" style="display: none;">
                        <button type="button" class="expand-btn" onclick="return toggleFeatures(event);">
                            <span id="expandText">展开</span>
                            <i class="fa fa-angle-down" id="expandIcon"></i>
                        </button>
                    </div>
                </div>
                <div class="section-title">选择订阅方式</div>
                
                <div class="pricing-section" id="pricingSection">
                    <!-- 价格选项将通过Ajax动态加载 -->
                </div>
            </div>

            <div class="right-section">
                <!-- 信任要素区域 -->
                <div class="trust-elements">
                    <div class="social-proof">
                        <div class="user-stats">
                            <i class="fa fa-users"></i>
                            <span>已有 <strong><span class="user-count">50,000+</span></strong> 用户选择升级</span>
                        </div>
                        <div class="rating-info">
                            <div class="stars">
                                <i class="fa fa-star"></i>
                                <i class="fa fa-star"></i>
                                <i class="fa fa-star"></i>
                                <i class="fa fa-star"></i>
                                <i class="fa fa-star"></i>
                            </div>
                            <span class="rating-text">4.8分 (2,341条评价)</span>
                        </div>

                        <div class="user-testimonials" style="margin-top:16px;height:68px;overflow:hidden;position:relative;">
                            <div class="testimonial-slider" id="testimonialSlider" style="transition:transform 0.5s ease;">
                                <div class="testimonial-item" style="height:60px;padding:12px;background:rgba(0,123,250,0.05);border-radius:8px;border-left:3px solid #007cfa;margin-bottom:8px;display:flex;flex-direction:column;justify-content:center;">
                                    <div style="font-size:13px;color:#333;line-height:1.4;margin-bottom:6px;">
                                        "识别准确率很高，大大提升了工作效率！"
                                    </div>
                                    <div style="font-size:11px;color:#666;text-align:right;">
                                        —— 来自北京的李**用户
                                    </div>
                                </div>
                                <div class="testimonial-item" style="height:60px;padding:12px;background:rgba(0,123,250,0.05);border-radius:8px;border-left:3px solid #007cfa;margin-bottom:8px;display:flex;flex-direction:column;justify-content:center;">
                                    <div style="font-size:13px;color:#333;line-height:1.4;margin-bottom:6px;">
                                        "批量处理文档太方便了，节省了大量时间！"
                                    </div>
                                    <div style="font-size:11px;color:#666;text-align:right;">
                                        —— 来自上海的王**用户
                                    </div>
                                </div>
                                <div class="testimonial-item" style="height:60px;padding:12px;background:rgba(0,123,250,0.05);border-radius:8px;border-left:3px solid #007cfa;margin-bottom:8px;display:flex;flex-direction:column;justify-content:center;">
                                    <div style="font-size:13px;color:#333;line-height:1.4;margin-bottom:6px;">
                                        "客服响应很快，技术支持很专业！"
                                    </div>
                                    <div style="font-size:11px;color:#666;text-align:right;">
                                        —— 来自广州的张**用户
                                    </div>
                                </div>
                                <div class="testimonial-item" style="height:60px;padding:12px;background:rgba(0,123,250,0.05);border-radius:8px;border-left:3px solid #007cfa;margin-bottom:8px;display:flex;flex-direction:column;justify-content:center;">
                                    <div style="font-size:13px;color:#333;line-height:1.4;margin-bottom:6px;">
                                        "表格识别功能太强大了，完全解放双手！"
                                    </div>
                                    <div style="font-size:11px;color:#666;text-align:right;">
                                        —— 来自深圳的陈**用户
                                    </div>
                                </div>
                                <div class="testimonial-item" style="height:60px;padding:12px;background:rgba(0,123,250,0.05);border-radius:8px;border-left:3px solid #007cfa;margin-bottom:8px;display:flex;flex-direction:column;justify-content:center;">
                                    <div style="font-size:13px;color:#333;line-height:1.4;margin-bottom:6px;">
                                        "多设备同步使用，办公效率翻倍！"
                                    </div>
                                    <div style="font-size:11px;color:#666;text-align:right;">
                                        —— 来自杭州的刘**用户
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="display:flex;flex-wrap:wrap;gap:8px;margin-top:16px;">
                        <div style="display:flex;align-items:center;color:#007cfa;font-size:12px;background:rgba(0,123,250,0.08);padding:6px 10px;border-radius:12px;border:1px solid rgba(0,123,250,0.15);">
                            <i class="fa fa-certificate" style="margin-right:4px;font-size:11px;"></i>正版授权
                        </div>
                        <div style="display:flex;align-items:center;color:#007cfa;font-size:12px;background:rgba(0,123,250,0.08);padding:6px 10px;border-radius:12px;border:1px solid rgba(0,123,250,0.15);">
                            <i class="fa fa-cloud-upload-alt" style="margin-right:4px;font-size:11px;"></i>服务稳定
                        </div>
                        <div style="display:flex;align-items:center;color:#007cfa;font-size:12px;background:rgba(0,123,250,0.08);padding:6px 10px;border-radius:12px;border:1px solid rgba(0,123,250,0.15);">
                            <i class="fa fa-users-cog" style="margin-right:4px;font-size:11px;"></i>专业团队
                        </div>
                    </div>
                </div>
                <div class="order-summary" id="orderSummary">
                    <div class="summary-item">
                        <div class="summary-label"><i class="fa fa-crown" style="margin-right:6px;color:#ffc107;"></i>升级方案</div>
                        <div class="summary-value" id="selectedPlan">加载中...</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label"><i class="fa fa-info-circle" style="margin-right:6px;color:#007cfa;"></i>服务说明</div>
                        <div class="summary-value" id="selectedDesc">加载中...</div>
                    </div>
                    <div class="summary-divider"></div>

                    <div class="order-total" style="background:linear-gradient(135deg,rgba(0,123,250,0.05) 0%,rgba(0,123,250,0.08) 100%);border:2px solid rgba(0,123,250,0.15);border-radius:16px;padding:10px 20px 10px 20px;margin:16px 0;position:relative;">
                        <div style="position:absolute;top:-8px;right:-2px;background:linear-gradient(135deg,#ff6600,#ff8533);color:white;padding:6px 12px;border-radius:12px;font-size:13px;font-weight:700;box-shadow:0 2px 8px rgba(255,102,0,0.3);border:1px solid rgba(255,255,255,0.2);" id="discountAmount">
                            <i class="fa fa-clock" style="margin-right:4px;animation:pulse 2s infinite;"></i>限时节省¥0
                        </div>
                        <div class="total-row" style="justify-content:center;">
                            <div class="total-prices">
                                <div style="display:flex;align-items:baseline;gap:16px;justify-content:center;">
                                    <span class="current-total" style="font-size:40px;font-weight:900;color:#007cfa;text-shadow:0 2px 6px rgba(0,123,250,0.3);line-height:1;">¥0</span>
                                    <span class="original-total" style="font-size:18px;color:#999;text-decoration:line-through;opacity:0.8;font-weight:600;">¥0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="account-section" id="accountSection" style="min-height:60px;margin:16px 0;display:none;">
                        <div class="account-input" id="accountInput">
                            <input type="text" id="txtAccount" placeholder="请输入您的账号（手机号/邮箱）" class="form-control" value="" />
                        </div>
                    </div>
                    <button type="button" class="btn-upgrade" id="upgradeBtn" onclick="submitUpgrade()" style="position:relative;overflow:hidden;margin:10px 0 12px 0;">
                        <span class="btn-text">立即升级</span>
                        <div class="btn-shine" style="position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.3),transparent);animation:shine 2s infinite;"></div>
                    </button>
                    <div class="service-guarantee" style="background:rgba(40,167,69,0.08);margin:10px 0;padding:16px;border-radius:12px;border-left:4px solid #28a745;">
                        <div style="color:#28a745;font-weight:600;font-size:14px;margin-bottom:12px;text-align:left;">
                            <i class="fa fa-shield-check" style="margin-right:6px;"></i>服务保障
                        </div>
                        <div style="display:flex;justify-content:center;flex-wrap:wrap;gap:8px;">
                            <div style="display:flex;align-items:center;color:#28a745;font-size:11px;background:rgba(40,167,69,0.1);padding:4px 8px;border-radius:12px;">
                                <i class="fa fa-bolt" style="margin-right:4px;font-size:10px;"></i>立即生效
                            </div>
                            <div style="display:flex;align-items:center;color:#28a745;font-size:11px;background:rgba(40,167,69,0.1);padding:4px 8px;border-radius:12px;">
                                <i class="fa fa-undo" style="margin-right:4px;font-size:10px;"></i>7天退款
                            </div>
                            <div style="display:flex;align-items:center;color:#28a745;font-size:11px;background:rgba(40,167,69,0.1);padding:4px 8px;border-radius:12px;">
                                <i class="fa fa-headset" style="margin-right:4px;font-size:10px;"></i>24h客服
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 功能对比弹框 -->
    <div id="compareModal" class="compare-modal">
        <div class="compare-modal-content">
            <div class="compare-modal-header">
                <h2 class="compare-modal-title">各版本功能比较</h2>
                <span class="compare-modal-close" onclick="closeCompareModal()">&times;</span>
            </div>
            <div class="compare-modal-body">
                <iframe id="compareIframe" src="desc.aspx" frameborder="0"></iframe>
            </div>
        </div>
    </div>

    <script>
var currentAccount='',phoneReg=/^1[3456789]\d{9}$/,emailReg=/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
var membershipData=null,userStatus=null,isExpanded=false;

// 数据加载处理
function loadPageData(){
    // 检查是否已有缓存的HTML内容
    var hasPrerenderedContent = document.getElementById('versionTabs').children.length > 0;

    // 始终加载数据以支持动态交互
    return Promise.all([
        loadMembershipData(),
        loadUserStatus()
    ]).then(function(){
        // 根据是否有预渲染内容决定是否重新渲染HTML
        if(!hasPrerenderedContent){
            renderVersionTabs();
            renderPricingOptions();
            console.log('完整加载页面数据和HTML');
        }else{
            console.log('使用缓存HTML，仅加载数据支持交互');
        }

        // 通用的初始化逻辑
        initializeDefaultSelection();
        updateCurrentVersionFeatures();
        updateOrderSummary();
    }).catch(function(error){
        console.error('数据加载失败:', error);
        if(hasPrerenderedContent){
            // 有缓存内容时，即使数据加载失败也要初始化基础功能
            initializeDefaultSelection();
        }else{
            // 没有缓存内容时显示错误
            showLoadingError();
            throw error;
        }
    });
}

// 加载会员数据
function loadMembershipData(){
    return fetch('User.ashx?op=getmembershipdata')
        .then(response=>response.json())
        .then(data=>{
            if(data.success){
                membershipData=data.data;
            }else{
                throw new Error(data.message||'获取会员数据失败');
            }
        });
}

// 加载用户状态
function loadUserStatus(){
    return fetch('User.ashx?op=getuserstatus')
        .then(response=>response.json())
        .then(data=>{
            if(data.success){
                userStatus=data.data;
                currentAccount=data.data.account||'';
            }else{
                throw new Error(data.message||'获取用户状态失败');
            }
        })
        .catch(error=>{
            // 用户状态加载失败时使用默认值
            userStatus={isLoggedIn:false,account:'',userType:'',userTypeHash:0};
            currentAccount='';
            console.warn('用户状态加载失败，使用默认值:', error);
        });
}

// 渲染版本选项卡
function renderVersionTabs(){
    if(!membershipData||!membershipData.membershipTypes)return;

    var tabsContainer=document.getElementById('versionTabs');
    var html='';

    membershipData.membershipTypes.forEach(function(type,index){
        var isActive=index===0?'active':'';
        var versionClass='v'+type.typeHash;
        var recommendBadge=type.recommendBadge?`<div class="version-recommend-badge">${type.recommendBadge}</div>`:'';

        html+=`
            <div class="version-tab ${versionClass} ${isActive}" data-type="${type.typeHash}" data-desc="${type.typeDesc}" onclick="selectVersion('${type.typeHash}')">
                ${recommendBadge}
                <div class="tab-icon ${type.iconClass}"></div>
                <div class="tab-name">${type.typeName}</div>
            </div>
        `;
    });

    tabsContainer.innerHTML=html;
}

// 渲染价格选项
function renderPricingOptions(){
    if(!membershipData||!membershipData.membershipTypes)return;

    var pricingContainer=document.getElementById('pricingSection');
    var html='';

    membershipData.membershipTypes.forEach(function(type,index){
        var isActive=index===0?'active':'';
        var packagesHtml='';

        type.packages.forEach(function(pkg){
            var isSelected=pkg.isDefault?'selected':'';
            var tagHtml=pkg.tag?getTagHtml(pkg.tag):'';
            var discountHtml=pkg.originalPrice>pkg.price?`<div class="discount-badge">${Math.round((pkg.price/pkg.originalPrice)*10*10)/10}折</div>`:'';
            var originalPriceHtml=pkg.originalPrice>pkg.price?`<span class="original-price">¥${pkg.originalPrice.toFixed(0)}</span><span class="discount-percentage">省¥${(pkg.originalPrice-pkg.price).toFixed(0)}</span>`:'';
            var dailyCost=calculateDailyCost(pkg.name,pkg.price);

            packagesHtml+=`
                <div class="pricing-item ${isSelected}" data-name="${pkg.name}" data-price="${pkg.price}" data-original="${pkg.originalPrice}" data-desc="${pkg.desc}" data-is-default="${pkg.isDefault}" onclick="selectPricing(this)">
                    ${tagHtml}
                    ${discountHtml}
                    <div class="pricing-name">${pkg.name}</div>
                    <div class="pricing-price">
                        <span class="current-price">¥${pkg.price.toFixed(0)}</span>
                        ${originalPriceHtml}
                    </div>
                    <div class="daily-cost">每天仅需¥${dailyCost.toFixed(2)}</div>
                </div>
            `;
        });

        html+=`<div class="pricing-options ${isActive}" data-type="${type.typeHash}">${packagesHtml}</div>`;
    });

    pricingContainer.innerHTML=html;
}

// 获取标签HTML
function getTagHtml(tag){
    var tagClass='',tagText='';
    switch(tag.toLowerCase()){
        case 'hot':tagClass='hot';tagText='热门';break;
        case '_new':tagClass='new';tagText='新';break;
        case 'recommond':tagClass='recommend';tagText='推荐';break;
        default:return '';
    }
    return `<div class="pricing-tag ${tagClass}">${tagText}</div>`;
}

// 计算每日成本
function calculateDailyCost(name,price){
    var days=365;
    if(name.includes('终身')){
        days=365*10;
    }else{
        var number=1;
        var match=name.match(/(\d+)/);
        if(match)number=parseInt(match[1]);
        else{
            var chineseNumbers='一二三四五六七八九十';
            for(var i=0;i<chineseNumbers.length;i++){
                if(name.includes(chineseNumbers[i])){
                    number=i+1;break;
                }
            }
        }
        if(name.includes('年'))days=365*number;
        else if(name.includes('月'))days=30*number;
        else if(name.includes('日')||name.includes('天'))days=number;
    }
    return price/days;
}

// 初始化默认选择
function initializeDefaultSelection(){
    // 显示/隐藏账号输入框
    var accountSection=document.getElementById('accountSection');
    if(userStatus&&!userStatus.isLoggedIn){
        accountSection.style.display='block';
    }else{
        accountSection.style.display='none';
    }

    // 设置账号输入框的值
    var accountInput=document.getElementById('txtAccount');
    if(accountInput&&currentAccount){
        accountInput.value=currentAccount;
    }
}

// 显示加载错误
function showLoadingError(){
    var tabsContainer=document.getElementById('versionTabs');
    var pricingContainer=document.getElementById('pricingSection');
    tabsContainer.innerHTML='<div style="text-align:center;color:#999;padding:20px;">数据加载失败，请刷新页面重试</div>';
    pricingContainer.innerHTML='<div style="text-align:center;color:#999;padding:20px;">价格信息加载失败</div>';
}

// 功能特性数据（保持向后兼容）
var versionCoreFeatures={'-1':[{name:'基础识别套装',desc:'本地识别+图片识别+区域识别+竖排识别'},{name:'翻译功能',desc:'划词翻译+图片翻译'},{name:'使用限制',desc:'每日200次，1秒/次'},{name:'设备授权',desc:'最多2台设备同时使用'},{name:'专属客服',desc:'优先技术支持'},{name:'需求定制',desc:'个性化功能定制'}],'1':[{name:'基础识别套装',desc:'包含v-1全部识别功能'},{name:'高级识别',desc:'公式识别+表格识别'},{name:'翻译功能',desc:'划词翻译+图片翻译'},{name:'性能提升',desc:'每日500次，1秒/2次'},{name:'多设备支持',desc:'最多3台设备同时使用'},{name:'专属客服',desc:'优先技术支持+需求定制'}],'3':[{name:'完整识别套装',desc:'包含v1全部功能'},{name:'文档处理',desc:'文档识别+文档翻译+批量识别'},{name:'多结果显示',desc:'多种识别结果对比'},{name:'极速体验',desc:'每日2000次，1秒/3次'},{name:'多设备授权',desc:'最多5台设备同时使用'},{name:'自选通道',desc:'多种识别引擎可选择'}]};
var versionScenarios={'-1':['扫描文档','提取图片文字','学习笔记整理','日常办公'],'1':['工作文档处理','表格数据提取','公式识别','团队协作'],'3':['批量文档处理','多格式转换','企业级应用','高频使用场景']};
function updateScenarios(versionType){var scenarios=versionScenarios[versionType]||[],scenarioTagsContainer=document.getElementById('scenarioTags');if(scenarioTagsContainer){var html='';scenarios.forEach(function(scenario){html+=`<span class="scenario-tag">${scenario}</span>`});scenarioTagsContainer.innerHTML=html}}
function updateCurrentVersionFeatures(){var selectedTab=document.querySelector('.version-tab.active'),typeHash=selectedTab.getAttribute('data-type'),versionClass=getVersionClass(selectedTab),typeDesc=selectedTab.getAttribute('data-desc');document.getElementById('featuresTitle').textContent=typeDesc;var featuresSection=document.getElementById('versionFeaturesSection');featuresSection.className=featuresSection.className.replace(/\bv-?\d+\b/g,'')+' '+versionClass;var features=versionCoreFeatures[typeHash]||[],featuresHtml='',displayCount=isExpanded?features.length:Math.min(4,features.length);for(var i=0;i<displayCount;i++){var feature=features[i];featuresHtml+='<div class="feature-item"><i class="fa fa-check"></i><div class="feature-content"><div class="feature-name">'+feature.name+'</div>';if(feature.desc)featuresHtml+='<div class="feature-desc">'+feature.desc+'</div>';featuresHtml+='</div></div>'}document.getElementById('featuresGrid').innerHTML=featuresHtml;var expandSection=document.getElementById('featuresExpand');if(features.length>4){expandSection.style.display='block';document.getElementById('expandText').textContent=isExpanded?'收起':'展开';var icon=document.getElementById('expandIcon');icon.className=isExpanded?'fa fa-angle-up':'fa fa-angle-down';document.querySelector('.expand-btn').classList.toggle('expanded',isExpanded)}else{expandSection.style.display='none'}updateScenarios(typeHash)}
function toggleFeatures(event){event?.preventDefault();event?.stopPropagation();isExpanded=!isExpanded;updateCurrentVersionFeatures();return false}
function getVersionClass(tab){var typeHash=tab.getAttribute('data-type');return typeHash?'v'+typeHash:'v1'}
document.addEventListener('DOMContentLoaded',function(){
    // 优先加载页面数据，然后初始化其他功能
    loadPageData().then(function(){
        initFormValidation();
        animateUserCount();
        startTestimonialSlider();
    }).catch(function(error){
        console.error('页面初始化失败:', error);
        // 即使数据加载失败，也要初始化基础功能
        initFormValidation();
        animateUserCount();
        startTestimonialSlider();
    });
});
function initFormValidation(){var accountInput=document.getElementById('txtAccount');if(accountInput){accountInput.addEventListener('input',function(){var account=this.value.trim(),isValid=phoneReg.test(account)||emailReg.test(account);this.style.borderColor=account===''?'#e9ecef':isValid?'#28a745':'#dc3545';if(account!==''&&!isValid){this.style.boxShadow='0 0 0 3px rgba(220,53,69,0.1)'}else if(isValid){this.style.boxShadow='0 0 0 3px rgba(40,167,69,0.1)'}else{this.style.boxShadow='none'}});accountInput.addEventListener('blur',function(){if(this.style.borderColor==='rgb(220, 53, 69)'){this.style.boxShadow='0 0 0 3px rgba(220,53,69,0.1)'}})}}
function animateUserCount(){var userCountElement=document.querySelector('.user-count');if(!userCountElement)return;var targetNumber=50000,currentNumber=0,startTime=Date.now(),duration=2000;function animate(){var elapsed=Date.now()-startTime,progress=Math.min(elapsed/duration,1);currentNumber=targetNumber*progress;userCountElement.textContent=Math.floor(currentNumber).toLocaleString()+'+';if(progress<1)requestAnimationFrame(animate)}requestAnimationFrame(animate)}
function startTestimonialSlider(){var slider=document.getElementById('testimonialSlider'),items=slider.querySelectorAll('.testimonial-item');if(items.length===0)return;var currentIndex=0,itemHeight=68;setInterval(function(){currentIndex=(currentIndex+1)%items.length;slider.style.transform='translateY(-'+currentIndex*itemHeight+'px)'},3000)}
function selectVersion(typeHash){var selectedTab=document.querySelector('.version-tab[data-type="'+typeHash+'"]');document.querySelectorAll('.version-tab').forEach(tab=>tab.classList.remove('active'));selectedTab.classList.add('active');document.querySelectorAll('.pricing-options').forEach(option=>option.classList.remove('active'));var targetPricingOptions=document.querySelector('.pricing-options[data-type="'+typeHash+'"]');targetPricingOptions.classList.add('active');document.querySelectorAll('.pricing-item').forEach(item=>item.classList.remove('selected'));var defaultPricing=targetPricingOptions.querySelector('.pricing-item[data-is-default="true"]');if(!defaultPricing){defaultPricing=targetPricingOptions.querySelector('.pricing-item')}if(defaultPricing){defaultPricing.classList.add('selected')}updateVersionTheme(selectedTab);updateOrderSummary();updateCurrentVersionFeatures()}
function updateVersionTheme(selectedTab){var versionClass=getVersionClass(selectedTab);document.querySelectorAll('.pricing-item').forEach(item=>{Array.from(item.classList).forEach(cls=>{if(cls.match(/^v-?\d+$/))item.classList.remove(cls)});item.classList.add(versionClass)});document.querySelectorAll('.btn-upgrade').forEach(btn=>{btn.className=btn.className.replace(/\bv-?\d+\b/g,'')+' '+versionClass});var featuresSection=document.getElementById('versionFeaturesSection');featuresSection.className=featuresSection.className.replace(/\bv-?\d+\b/g,'')+' '+versionClass}
function selectPricing(element){document.querySelectorAll('.pricing-item').forEach(item=>item.classList.remove('selected'));element.classList.add('selected');var selectedTab=document.querySelector('.version-tab.active'),versionClass=getVersionClass(selectedTab);document.querySelectorAll('.pricing-item').forEach(item=>{Array.from(item.classList).forEach(cls=>{if(cls.match(/^v-?\d+$/))item.classList.remove(cls)});item.classList.add(versionClass)});updateOrderSummary()}
function updateOrderSummary(){var selectedVersionTab=document.querySelector('.version-tab.active'),selectedVersion=selectedVersionTab.querySelector('.tab-name').textContent,selectedPricing=document.querySelector('.pricing-item.selected');if(!selectedPricing)return;var planName=selectedPricing.getAttribute('data-name'),price=parseFloat(selectedPricing.getAttribute('data-price')),originalPrice=parseFloat(selectedPricing.getAttribute('data-original')),desc=selectedPricing.getAttribute('data-desc'),discount=(originalPrice-price).toFixed(0);var elements={selectedPlan:document.getElementById('selectedPlan'),selectedDesc:document.getElementById('selectedDesc'),discountInfo:document.querySelector('.discount-info'),originalTotal:document.querySelector('.original-total'),currentTotal:document.querySelector('.current-total'),discountAmount:document.getElementById('discountAmount')};if(elements.selectedPlan)elements.selectedPlan.textContent=planName+selectedVersion;if(elements.selectedDesc)elements.selectedDesc.textContent=desc||'';if(elements.discountInfo)elements.discountInfo.textContent=`已优惠: ¥${discount}`;if(elements.originalTotal)elements.originalTotal.textContent=`¥${originalPrice.toFixed(0)}`;if(elements.currentTotal)elements.currentTotal.textContent=`¥${price.toFixed(0)}`;if(elements.discountAmount)elements.discountAmount.innerHTML=`<i class="fa fa-clock" style="margin-right:4px;animation:pulse 2s infinite;"></i>限时节省¥${discount}`}
function submitUpgrade(){var selectedVersionTab=document.querySelector('.version-tab.active'),selectedVersion=selectedVersionTab.querySelector('.tab-name').textContent,selectedPricing=document.querySelector('.pricing-item.selected');if(!selectedPricing){alert('请选择订阅方式！');return}var accountInput=document.getElementById('txtAccount'),account=currentAccount||(accountInput?accountInput.value.trim():'');if(!account){var accountSection=document.getElementById('accountInput');if(accountSection)accountSection.style.display='block';alert('请输入您要升级的账号（手机号/邮箱）！');if(accountInput)accountInput.focus();return}if(!phoneReg.test(account)&&!emailReg.test(account)){alert("账号格式有误（手机号/邮箱），请检查后重试！");if(accountInput)accountInput.focus();return}var upgradeBtn=document.getElementById('upgradeBtn'),btnText=upgradeBtn.querySelector('.btn-text');upgradeBtn.classList.add('loading');btnText.textContent='处理中...';upgradeBtn.disabled=true;var planName=selectedPricing.getAttribute('data-name'),upgradeType=planName+selectedVersion;fetch("code.aspx?op=pay&remark="+encodeURIComponent(upgradeType)+"&account="+encodeURIComponent(account)).then(response=>response.text()).then(result=>{if(result.indexOf('http')!==-1){window.location.href=result.replace("http:","").replace("https:","")}else{alert(result);resetUpgradeButton()}}).catch((error)=>{alert('请求失败，请稍后重试！');resetUpgradeButton()})}
function resetUpgradeButton(){var upgradeBtn=document.getElementById('upgradeBtn'),btnText=upgradeBtn.querySelector('.btn-text');upgradeBtn.classList.remove('loading');btnText.textContent='立即升级';upgradeBtn.disabled=false}
function openCompareModal(){toggleCompareModal(true)}
function closeCompareModal(){toggleCompareModal(false)}
function toggleCompareModal(show){var modal=document.getElementById('compareModal');modal.style.display=show?'block':'none';document.body.style.overflow=show?'hidden':'auto'}
window.onclick=function(event){var modal=document.getElementById('compareModal');if(event.target==modal){closeCompareModal()}}
document.addEventListener('keydown',function(event){if(event.key==='Escape'){closeCompareModal()}});
function openSmartRecommendationIframe(){var iframeHtml='<div id="smartRecommendationModal" style="position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.6);z-index:1000;display:flex;align-items:center;justify-content:center;backdrop-filter:blur(3px);"><div style="background:transparent;border-radius:20px;padding:0;max-width:600px;width:90%;display:flex;flex-direction:column;box-shadow:0 20px 60px rgba(0,0,0,0.3);position:relative;overflow:hidden;"><iframe src="SmartRecommendation.aspx" style="width:100%;height:90vh;border:none;border-radius:20px;background:transparent;" frameborder="0"></iframe></div></div>';document.body.insertAdjacentHTML('beforeend',iframeHtml);document.body.style.overflow='hidden';window.addEventListener('message',handleRecommendationMessage)}
function closeSmartRecommendationIframe(){var modal=document.getElementById('smartRecommendationModal');if(modal){modal.remove();document.body.style.overflow='auto';window.removeEventListener('message',handleRecommendationMessage)}}
function handleRecommendationMessage(event){if(event.origin!==window.location.origin)return;var data=event.data;switch(data.action){case 'closeRecommendation':closeSmartRecommendationIframe();break;case 'handleRecommendation':closeSmartRecommendationIframe();var pendingRecommendation=data.pendingRecommendation;if(pendingRecommendation){if(!pendingRecommendation.userHasSelected){switchToVersionWithToast(pendingRecommendation.version,true)}else if(pendingRecommendation.isApplyRecommendation){switchToVersionWithToast(pendingRecommendation.version,true)}else if(pendingRecommendation.selectedVersion){switchToVersionWithToast(pendingRecommendation.selectedVersion,false)}}break}}
function switchToVersionWithToast(versionType,showToast){var typeHash=versionType.substring(1),versionTab=Array.from(document.querySelectorAll('.version-tab')).find(function(tab){return tab.getAttribute('data-type')===typeHash});if(versionTab){if(!versionTab.classList.contains('active')){versionTab.click()}if(showToast){var versionName=versionTab.querySelector('.tab-name').textContent;showRecommendationToast(versionName)}versionTab.scrollIntoView({behavior:'smooth',block:'center'})}}
function showRecommendationToast(versionName){var existingToast=document.querySelector('.recommendation-toast');if(existingToast)existingToast.remove();var toast=document.createElement('div');toast.className='recommendation-toast';toast.style.cssText='position:fixed;top:20px;right:20px;background:linear-gradient(135deg,#28a745 0%,#20c997 100%);color:white;padding:16px 20px;border-radius:12px;font-size:14px;font-weight:600;z-index:10000;box-shadow:0 8px 24px rgba(40,167,69,0.3);max-width:300px;opacity:0;transform:translateX(100%);transition:all 0.3s ease';toast.innerHTML=`<div style="display:flex;align-items:center;gap:8px;"><i class="fa fa-check-circle" style="font-size:16px;"></i><div><div>已为您选择 ${versionName}</div><div style="font-size:12px;opacity:0.9;margin-top:2px;">基于AI智能分析推荐</div></div></div>`;document.body.appendChild(toast);setTimeout(()=>{toast.style.opacity='1';toast.style.transform='translateX(0)'},10);setTimeout(()=>{toast.style.opacity='0';toast.style.transform='translateX(100%)';setTimeout(()=>{if(toast.parentNode)toast.remove()},300)},4000)}
    </script>
</asp:Content>
