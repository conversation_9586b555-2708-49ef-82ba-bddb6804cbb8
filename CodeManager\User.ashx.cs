using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Web;
using Account.Web.Common;
using CommonLib;
using Newtonsoft.Json;

namespace Account.Web
{
    /// <summary>
    /// 用户相关操作处理器（登录、注册、用户信息修改等）
    /// </summary>
    public class User : I<PERSON>ttpHandler, IHttpAsyncHandler
    {
        public bool IsReusable => false;

        public void ProcessRequest(HttpContext context)
        {
            // 同步方法不实现，使用异步方法
        }

        public async Task ProcessRequestAsync(HttpContext context)
        {
            try
            {
                // 添加Connection: keep-alive到响应头
                context.Response.AddHeader("Connection", "keep-alive");
                context.Response.AddHeader("Keep-Alive", "timeout=3600");
                context.Response.ContentType = "application/json";

                // CORS处理
                context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
                context.Response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
                context.Response.Headers.Add("Access-Control-Allow-Headers", "Content-Type");

                if (context.Request.HttpMethod == "OPTIONS")
                {
                    context.Response.StatusCode = 200;
                    return;
                }

                string action = context.Request.QueryString["op"]?.ToLower() ?? "";

                switch (action)
                {
                    case "login":
                        await Task.Run(() => HandleLogin(context));
                        break;
                    case "register":
                        await Task.Run(() => HandleRegister(context));
                        break;
                    case "resetpwd":
                        await Task.Run(() => HandleResetPassword(context));
                        break;
                    case "updatepwd":
                        await Task.Run(() => HandleUpdatePassword(context));
                        break;
                    case "updatenickname":
                        await Task.Run(() => HandleUpdateNickname(context));
                        break;
                    case "getinfo":
                        await Task.Run(() => HandleGetUserInfo(context));
                        break;
                    case "userinfo":
                        await Task.Run(() => HandleUserInfoHtml(context));
                        break;
                    case "getdevices":
                        await Task.Run(() => HandleGetDevices(context));
                        break;
                    case "deviceaction":
                        await Task.Run(() => HandleDeviceAction(context));
                        break;
                    case "getorders":
                        await Task.Run(() => HandleGetOrders(context));
                        break;
                    case "logout":
                        await Task.Run(() => HandleLogout(context));
                        break;
                    case "getmembershipdata":
                        await Task.Run(() => HandleGetMembershipData(context));
                        break;
                    case "getuserstatus":
                        await Task.Run(() => HandleGetUserStatus(context));
                        break;
                    default:
                        ResponseService.WriteJsonResponse(context, false, "不支持的操作");
                        break;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Log.Error("User.ashx处理异常", ex);
                ResponseService.WriteJsonResponse(context, false, "服务器内部错误");
            }
        }

        #region 用户认证相关

        /// <summary>
        /// 用户登录
        /// </summary>
        private void HandleLogin(HttpContext context)
        {
            try
            {
                ResponseService.InitializeJsonResponse(context);

                string username = context.Request.Form["username"]?.Trim();
                string password = context.Request.Form["password"]?.Trim();
                var lang = context.Request.GetValue("lang");

                var identity = Guid.NewGuid().ToString().Replace("-", "");

                // 使用统一的登录服务
                var result = UnifiedUserService.LoginUser(username, password, identity, true, lang);

                if (result.IsSuccess)
                {
                    var loginInfo = result.Data;
                    AuthHelper.SetUserSession(context.Response, context.Request, loginInfo.Token, loginInfo.Account);
                    ResponseService.WriteJsonResponse(context, true, "登录成功", new { redirectUrl = "User.aspx" });
                }
                else
                {
                    ResponseService.WriteJsonResponse(context, false, result.Message);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Log.Error("用户登录异常", ex);
                ResponseService.WriteJsonResponse(context, false, "登录失败，请重试");
            }
        }

        /// <summary>
        /// 用户注册
        /// </summary>
        private void HandleRegister(HttpContext context)
        {
            try
            {
                ResponseService.InitializeJsonResponse(context);

                string account = context.Request.Form["account"]?.Trim();
                string password = context.Request.Form["password"]?.Trim();
                string verify = context.Request.Form["verify"]?.Trim();
                string nickname = context.Request.Form["nickname"]?.Trim();
                var lang = context.Request.GetValue("lang");

                // 使用统一的注册服务
                var result = UnifiedUserService.RegisterUser(account, password, nickname, verify, lang);

                if (result.IsSuccess)
                {
                    ResponseService.WriteJsonResponse(context, true, UserConst.StrRegSuccess.GetTrans(lang), new { redirectUrl = "Login.aspx" });
                }
                else
                {
                    ResponseService.WriteJsonResponse(context, false, result.Message);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Log.Error("用户注册异常", ex);
                ResponseService.WriteJsonResponse(context, false, "注册失败，请重试");
            }
        }

        /// <summary>
        /// 重置密码（忘记密码）
        /// </summary>
        private void HandleResetPassword(HttpContext context)
        {
            try
            {
                ResponseService.InitializeJsonResponse(context);

                string account = context.Request.Form["account"]?.Trim();
                string password = context.Request.Form["password"]?.Trim();
                string verify = context.Request.Form["verify"]?.Trim();
                var lang = context.Request.GetValue("lang");

                // 使用统一的密码重置服务
                var result = UnifiedUserService.ResetPassword(account, password, verify, lang);

                if (result.IsSuccess)
                {
                    ResponseService.WriteJsonResponse(context, true, UserConst.StrResetPwdSuccess.GetTrans(lang), new { redirectUrl = "Login.aspx" });
                }
                else
                {
                    ResponseService.WriteJsonResponse(context, false, result.Message);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Log.Error("密码重置异常", ex);
                ResponseService.WriteJsonResponse(context, false, "密码重置失败，请重试");
            }
        }

        #endregion

        #region 用户信息管理

        /// <summary>
        /// 获取用户信息
        /// </summary>
        private void HandleGetUserInfo(HttpContext context)
        {
            ExecuteWithLogin(context, (ctx, session) =>
            {
                var userInfo = new
                {
                    account = session.Account,
                    nickname = session.NickName,
                    userType = session.UserType.ToString(),
                    dtReg = session.DtReg.ToString("yyyy-MM-dd"),
                    dtExpired = session.DtExpired.ToString("yyyy-MM-dd"),
                    remark = session.Remark
                };

                ResponseService.WriteJsonResponse(ctx, true, "获取成功", userInfo);
            }, true);
        }

        /// <summary>
        /// 获取用户信息HTML片段（用于页面显示）
        /// </summary>
        private void HandleUserInfoHtml(HttpContext context)
        {
            try
            {
                var session = GetUserSession(context);
                context.Response.ContentType = "text/html";

                // 使用与原始Code.ashx.cs完全相同的样式定义
                const string primaryButtonStyle = "display:inline-block; box-sizing:border-box; min-width:80px; height:33px; line-height:33px; font-size:14px; border-radius:4px; margin:0; text-align:center; background-color:#0d6efd; color:#fff; border:none; margin-right:10px; font-weight:normal; text-decoration:none; padding:0 15px;";
                const string logoutStyle = "display:inline-block; box-sizing:border-box; font-size:14px; margin:0; text-align:center; color:#212529; border:none; text-decoration:none; height:33px; line-height:33px;";

                if (session != null && !string.IsNullOrEmpty(session.Account))
                {
                    context.Response.Write($"<span style=\"display:flex; align-items:center; white-space:nowrap;\"><a class=\"pc_register\" style=\"{primaryButtonStyle}\" href=\"User.aspx\">个人中心</a><a class=\"pc_login\" style=\"{logoutStyle}\" href=\"Default.aspx?op=logout\">注销</a></span>");
                }
                else
                {
                    context.Response.Write($"<a class=\"pc_register\" style=\"{primaryButtonStyle}\" href=\"User.aspx\">登录</a>");
                }
                context.Response.End();
            }
            catch (Exception ex)
            {
                LogHelper.Log.Error("获取用户信息HTML异常", ex);
                context.Response.Write("加载失败");
            }
        }

        /// <summary>
        /// 修改密码（已登录用户）
        /// </summary>
        private void HandleUpdatePassword(HttpContext context)
        {
            ResponseService.InitializeJsonResponse(context);

            ExecuteWithLogin(context, (ctx, session) =>
            {
                var requestBody = new StreamReader(ctx.Request.InputStream).ReadToEnd();
                var requestData = JsonConvert.DeserializeObject<dynamic>(requestBody);
                string password = requestData?.password?.ToString();

                // 使用统一的密码验证
                if (!PasswordService.ValidatePassword(password))
                {
                    ResponseService.WriteJsonResponse(ctx, false, "密码必须为6-16位的数字或大小写字母！");
                    return;
                }

                // 使用统一的密码更新
                if (PasswordService.UpdateUserPassword(session.Account, password))
                {
                    UserLoginInfoHelper.LoginOut(ctx);
                    ResponseService.WriteJsonResponse(ctx, true, "密码修改成功，请使用新密码登录！", new { needLogin = true });
                }
                else
                {
                    ResponseService.WriteJsonResponse(ctx, false, "修改密码失败，请联系客服协助！");
                }
            }, true);
        }

        /// <summary>
        /// 修改昵称
        /// </summary>
        private void HandleUpdateNickname(HttpContext context)
        {
            ExecuteWithLogin(context, (ctx, session) =>
            {
                var requestBody = new StreamReader(ctx.Request.InputStream).ReadToEnd();
                var requestData = JsonConvert.DeserializeObject<dynamic>(requestBody);
                string nickName = requestData?.nickname?.ToString();

                // 使用统一的昵称验证逻辑（2-20位）
                if (!AuthHelper.IsValidNickName(nickName))
                {
                    WriteWebJsonResponse(ctx, false, "昵称为2-20位的中英文数字字母或下划线！");
                    return;
                }

                // 使用统一的昵称修改服务（内部会验证格式并更新缓存）
                var result = UnifiedUserService.UpdateNickname(session.Account, nickName);

                if (result.IsSuccess)
                {
                    // 统一服务已经更新了缓存，这里只需要更新当前会话
                    session.NickName = nickName;
                    WriteWebJsonResponse(ctx, true, "昵称修改成功！", new { nickname = nickName });
                }
                else
                {
                    WriteWebJsonResponse(ctx, false, result.Message);
                }
            });
        }

        #endregion

        #region 设备管理

        /// <summary>
        /// 获取设备列表
        /// </summary>
        private void HandleGetDevices(HttpContext context)
        {
            ExecuteWithLogin(context, (ctx, session) =>
            {
                var dtTmp = CodeHelper.GetOnLineCode(session.Account, "", DateTime.Now.AddDays(-90).ToString("yyyy-MM-dd"));
                var devices = new List<object>();

                if (dtTmp != null && dtTmp.Rows.Count > 0)
                {
                    foreach (System.Data.DataRow item in dtTmp.Rows)
                    {
                        devices.Add(new
                        {
                            uid = item["uid"]?.ToString() ?? "",
                            mac = item["Mac"]?.ToString() ?? "",
                            ip = item["Ip"]?.ToString() ?? "",
                            lastActivity = BoxUtil.GetDateTimeFromObject(item["活动时间"]).ToString("yyyy-MM-dd"),
                            state = item["state"]?.ToString() ?? "0"
                        });
                    }
                }

                WriteWebJsonResponse(ctx, true, "获取成功", devices);
            });
        }

        /// <summary>
        /// 设备操作（启用/禁用）
        /// </summary>
        private void HandleDeviceAction(HttpContext context)
        {
            ExecuteWithLogin(context, (ctx, session) =>
            {
                var requestBody = new StreamReader(ctx.Request.InputStream).ReadToEnd();
                var requestData = JsonConvert.DeserializeObject<dynamic>(requestBody);
                string action = requestData?.action?.ToString();
                string uid = requestData?.uid?.ToString();

                if (string.IsNullOrEmpty(action) || string.IsNullOrEmpty(uid) || !BoxUtil.IsAlphaNumeric(uid))
                {
                    WriteWebJsonResponse(ctx, false, "参数错误！");
                    return;
                }

                bool enableDevice = action == "enable";
                bool result = CodeHelper.UpdateUserDataState(session.Account, uid, enableDevice);

                if (!enableDevice && result)
                {
                    var token = CodeHelper.GetUserToken(session.Account, uid);
                    if (!string.IsNullOrEmpty(token))
                        RdsCacheHelper.LstAccountCache.ClearToken(session.Account, token);
                }

                string message = "设备" + (enableDevice ? "启用" : "禁用") + (result ? "成功" : "失败") + "！";
                WriteWebJsonResponse(ctx, result, message, new { action = action, uid = uid });
            });
        }

        #endregion

        #region 订单管理

        /// <summary>
        /// 获取订单列表
        /// </summary>
        private void HandleGetOrders(HttpContext context)
        {
            ExecuteWithLogin(context, (ctx, session) =>
            {
                string startDate = DateTime.Now.AddDays(-180).ToString("yyyy-MM-dd") + " 00:00:00";
                var dtTmp = PayHelper.GetPayList(startDate, session.Account);
                var orders = new List<object>();

                if (dtTmp != null && dtTmp.Rows.Count > 0)
                {
                    foreach (System.Data.DataRow item in dtTmp.Rows)
                    {
                        if (orders.Count >= 10) break;

                        var remark = BoxUtil.GetStringFromObject(item["remark"]).Replace("OCR助手-", "");
                        orders.Add(new
                        {
                            orderId = item["orderId"]?.ToString() ?? "",
                            remark = remark,
                            reallyPrice = item["reallyPrice"]?.ToString() ?? "0",
                            payType = item["type"]?.ToString() ?? "",
                            state = item["state"]?.ToString() ?? "",
                            createDate = BoxUtil.GetDateTimeFromObject(item["createDate"]).ToString("yyyy-MM-dd HH:mm")
                        });
                    }
                }

                WriteWebJsonResponse(ctx, true, "获取成功", orders);
            });
        }

        #endregion

        #region 会话管理

        /// <summary>
        /// 用户登出
        /// </summary>
        private void HandleLogout(HttpContext context)
        {
            try
            {
                context.Response.ContentType = "application/json";
                context.Response.Clear();

                UserLoginInfoHelper.LoginOut(context);
                WriteWebJsonResponse(context, true, "登出成功", new { redirectUrl = "Login.aspx" });
            }
            catch (Exception ex)
            {
                LogHelper.Log.Error("登出异常", ex);
                WriteWebJsonResponse(context, false, "登出失败");
            }
        }

        #endregion

        #region 会员数据接口

        /// <summary>
        /// 获取会员类型和价格数据
        /// </summary>
        private void HandleGetMembershipData(HttpContext context)
        {
            try
            {
                ResponseService.InitializeJsonResponse(context);

                var lstUserTypes = UserTypeHelper.GetCanRegUserTypes();
                var membershipTypes = new List<object>();

                foreach (var userType in lstUserTypes)
                {
                    var typeHash = userType.Type.GetHashCode();
                    var iconClass = "icon-v" + typeHash;

                    // 获取版本描述和推荐标签
                    string typeDesc = "";
                    string recommendBadge = "";
                    switch (typeHash)
                    {
                        case -1:
                            typeDesc = "个人专属，轻松识别";
                            recommendBadge = "最多选择";
                            break;
                        case 1:
                            typeDesc = "专业高效，精确稳定";
                            recommendBadge = "推荐";
                            break;
                        case 3:
                            typeDesc = "旗舰体验，最佳选择";
                            recommendBadge = "热门";
                            break;
                        default:
                            typeDesc = "专业服务，品质保证";
                            recommendBadge = "";
                            break;
                    }

                    // 构建价格选项列表
                    var packages = new List<object>();
                    if (userType.ChargeTypes != null)
                    {
                        foreach (var q in userType.ChargeTypes)
                        {
                            string strDesc = "";
                            var price = q.GetPrice(userType.PerPrice, ref strDesc);
                            packages.Add(new
                            {
                                name = q.Name,
                                desc = strDesc,
                                price = (double)price,
                                originalPrice = (double)q.OriPrice,
                                isDefault = q.IsDefault,
                                tag = q.Tag ?? ""
                            });
                        }
                    }

                    membershipTypes.Add(new
                    {
                        typeHash = typeHash,
                        typeName = userType.Type.ToString(),
                        typeDesc = typeDesc,
                        recommendBadge = recommendBadge,
                        iconClass = iconClass,
                        packages = packages
                    });
                }

                ResponseService.WriteJsonResponse(context, true, "获取成功", new { membershipTypes = membershipTypes });
            }
            catch (Exception ex)
            {
                LogHelper.Log.Error("获取会员数据异常", ex);
                ResponseService.WriteJsonResponse(context, false, "获取会员数据失败");
            }
        }

        /// <summary>
        /// 获取用户状态信息
        /// </summary>
        private void HandleGetUserStatus(HttpContext context)
        {
            try
            {
                ResponseService.InitializeJsonResponse(context);

                var currentUser = AuthHelper.GetUserSession(context.Request);
                var isLoggedIn = currentUser != null && !string.IsNullOrEmpty(currentUser.Account);
                var currentAccount = isLoggedIn ? currentUser.Account : "";

                var userStatus = new
                {
                    isLoggedIn = isLoggedIn,
                    account = currentAccount,
                    userType = isLoggedIn ? currentUser.UserType.ToString() : "",
                    userTypeHash = isLoggedIn ? currentUser.UserType.GetHashCode() : 0
                };

                ResponseService.WriteJsonResponse(context, true, "获取成功", userStatus);
            }
            catch (Exception ex)
            {
                LogHelper.Log.Error("获取用户状态异常", ex);
                ResponseService.WriteJsonResponse(context, false, "获取用户状态失败");
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 获取用户会话信息
        /// </summary>
        private UserLoginInfo GetUserSession(HttpContext context)
        {
            return AuthHelper.GetUserSession(context.Request);
        }

        /// <summary>
        /// 写入Web JSON响应（保持与原Code.ashx.cs一致）
        /// </summary>
        private void WriteWebJsonResponse(HttpContext context, bool success, string message, object data = null)
        {
            ResponseService.WriteJsonResponse(context, success, message, data);
        }

        /// <summary>
        /// 执行需要登录的操作（统一登录验证）
        /// </summary>
        private void ExecuteWithLogin(HttpContext context, Action<HttpContext, UserLoginInfo> action, bool useResponseService = false)
        {
            try
            {
                if (!useResponseService)
                {
                    context.Response.ContentType = "application/json";
                    context.Response.Clear();
                }

                var session = GetUserSession(context);
                if (session == null)
                {
                    if (useResponseService)
                        ResponseService.WriteJsonResponse(context, false, "请先登录");
                    else
                        WriteWebJsonResponse(context, false, "请先登录");
                    return;
                }

                action(context, session);
            }
            catch (Exception ex)
            {
                LogHelper.Log.Error("用户操作异常", ex);
                if (useResponseService)
                    ResponseService.WriteJsonResponse(context, false, "操作失败，请重试！");
                else
                    WriteWebJsonResponse(context, false, "操作失败，请重试！");
            }
        }

        #endregion

        #region IHttpAsyncHandler 实现

        public IAsyncResult BeginProcessRequest(HttpContext context, AsyncCallback cb, object extraData)
        {
            var task = ProcessRequestAsync(context);
            task.ContinueWith(t => cb?.Invoke(t));
            return task;
        }

        public void EndProcessRequest(IAsyncResult result)
        {
            if (result is Task task)
            {
                task.Dispose();
            }
        }

        #endregion
    }
}
